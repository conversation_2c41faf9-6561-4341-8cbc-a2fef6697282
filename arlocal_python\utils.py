"""
Utility functions for ArLocal Python
"""

import random
import string
import re
import os
import json
import hashlib
import base64
from typing import Any, Dict, List, Optional


def random_id(length: int = 43) -> str:
    """Generate a random ID similar to Arweave transaction IDs"""
    chars = string.ascii_letters + string.digits + '-_'
    return ''.join(random.choice(chars) for _ in range(length))


def sha256_b64url(data: bytes) -> str:
    """Calculate SHA256 hash and return as base64url encoded string"""
    hash_bytes = hashlib.sha256(data).digest()
    # Convert to base64url (no padding, URL-safe characters)
    b64 = base64.b64encode(hash_bytes).decode('ascii')
    return b64.replace('+', '-').replace('/', '_').rstrip('=')


def b64url_to_bytes(b64url_str: str) -> bytes:
    """Convert base64url string to bytes"""
    # Add padding if needed
    padding = 4 - (len(b64url_str) % 4)
    if padding != 4:
        b64url_str += '=' * padding

    # Convert URL-safe characters back to standard base64
    b64_str = b64url_str.replace('-', '+').replace('_', '/')
    return base64.b64decode(b64_str)


def calculate_transaction_id(signature: str) -> str:
    """Calculate transaction ID from signature (SHA256 hash of signature)"""
    try:
        signature_bytes = b64url_to_bytes(signature)
        return sha256_b64url(signature_bytes)
    except Exception as e:
        print(f"Error calculating transaction ID: {e}")
        return random_id(43)  # Fallback to random ID


def validate_txid(txid: str) -> bool:
    """Validate transaction ID format"""
    return bool(re.match(r'^[a-zA-Z0-9_-]{43}$', txid))


def validate_address(address: str) -> bool:
    """Validate wallet address format"""
    return bool(re.match(r'^[a-zA-Z0-9_-]{43}$', address))


def calculate_price(bytes_size: int, price_per_kb: int) -> int:
    """Calculate transaction price based on data size"""
    return round((bytes_size / 1000) * price_per_kb)


def save_transaction_data(data_dir: str, txid: str, data: Any) -> bool:
    """Save transaction data to disk"""
    try:
        if data_dir == ':memory:':
            return True  # Skip file operations for in-memory mode
            
        filepath = os.path.join(data_dir, f'data-{txid}')
        
        if isinstance(data, (dict, list)):
            data_str = json.dumps(data)
        elif isinstance(data, bytes):
            with open(filepath, 'wb') as f:
                f.write(data)
            return True
        else:
            data_str = str(data)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(data_str)
        
        return True
    except Exception as e:
        print(f"Error saving transaction data: {e}")
        return False


def load_transaction_data(data_dir: str, txid: str) -> Optional[Any]:
    """Load transaction data from disk"""
    try:
        if data_dir == ':memory:':
            return None  # No file operations for in-memory mode
            
        filepath = os.path.join(data_dir, f'data-{txid}')
        
        if not os.path.exists(filepath):
            return None
        
        # Try to read as binary first
        try:
            with open(filepath, 'rb') as f:
                return f.read()
        except:
            # Fall back to text
            with open(filepath, 'r', encoding='utf-8') as f:
                return f.read()
    except Exception as e:
        print(f"Error loading transaction data: {e}")
        return None


def delete_transaction_data(data_dir: str, txid: str) -> bool:
    """Delete transaction data from disk"""
    try:
        if data_dir == ':memory:':
            return True  # Skip file operations for in-memory mode
            
        filepath = os.path.join(data_dir, f'data-{txid}')
        
        if os.path.exists(filepath):
            os.remove(filepath)
        
        return True
    except Exception as e:
        print(f"Error deleting transaction data: {e}")
        return False


def format_transaction_for_response(tx_row: Any) -> Dict[str, Any]:
    """Format transaction row for API response"""
    if not tx_row:
        return {}
    
    # Convert sqlite3.Row to dict
    tx = dict(tx_row)
    
    # Parse JSON fields
    if tx.get('tags'):
        try:
            tx['tags'] = json.loads(tx['tags'])
        except (json.JSONDecodeError, TypeError):
            tx['tags'] = []
    else:
        tx['tags'] = []
    
    # Ensure data_size is string for compatibility
    if tx.get('data_size') is not None:
        tx['data_size'] = str(tx['data_size'])
    
    return tx


def format_block_for_response(block_row: Any) -> Dict[str, Any]:
    """Format block row for API response"""
    if not block_row:
        return {}
    
    # Convert sqlite3.Row to dict
    block = dict(block_row)
    
    # Parse JSON fields
    if block.get('txs'):
        try:
            block['txs'] = json.loads(block['txs'])
        except (json.JSONDecodeError, TypeError):
            block['txs'] = []
    else:
        block['txs'] = []
    
    if block.get('extended'):
        try:
            block['extended'] = json.loads(block['extended'])
        except (json.JSONDecodeError, TypeError):
            block['extended'] = {}
    
    # Convert mined_at to seconds for API compatibility
    if block.get('mined_at'):
        block['timestamp'] = block['mined_at'] // 1000
    
    return block


def tags_to_json(tags: List[Dict[str, str]]) -> str:
    """Convert tags list to JSON string for database storage"""
    return json.dumps(tags) if tags else '[]'


def json_to_tags(tags_json: str) -> List[Dict[str, str]]:
    """Convert JSON string to tags list"""
    try:
        return json.loads(tags_json) if tags_json else []
    except (json.JSONDecodeError, TypeError):
        return []
